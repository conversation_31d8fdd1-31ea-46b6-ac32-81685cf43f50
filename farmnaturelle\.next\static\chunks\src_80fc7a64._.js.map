{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/farmnaturelle/src/components/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport Image from 'next/image';\n\nexport default function HeroSection() {\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-neutral-50 to-neutral-100\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-5\">\n        <div className=\"absolute inset-0 bg-gradient-to-br from-primary-100/20 to-secondary-100/20\"></div>\n      </div>\n\n      <div className=\"relative max-w-7xl mx-auto px-6 lg:px-8 py-24 lg:py-32\">\n        <div className=\"grid lg:grid-cols-2 gap-12 lg:gap-16 items-center\">\n          {/* Content */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, ease: \"easeOut\" }}\n            className=\"text-center lg:text-left\"\n          >\n            <motion.h1\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.2 }}\n              className=\"font-serif text-4xl sm:text-5xl lg:text-6xl font-bold text-neutral-800 leading-tight\"\n            >\n              Arogya Saram\n            </motion.h1>\n            \n            <motion.p\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.4 }}\n              className=\"mt-4 text-xl lg:text-2xl text-primary-600 font-medium\"\n            >\n              South India's Heritage of Health\n            </motion.p>\n\n            <motion.p\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.6 }}\n              className=\"mt-6 text-lg text-neutral-600 max-w-2xl mx-auto lg:mx-0\"\n            >\n              Discover authentic wellness products rooted in ancient South Indian traditions. \n              From pure A2 Bilona Ghee to Himalayan Shilajit, we bring you nature's finest \n              gifts, crafted with time-honored methods for modern wellness.\n            </motion.p>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.8 }}\n              className=\"mt-10 flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\"\n            >\n              <Link\n                href=\"/products\"\n                className=\"inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white bg-primary-500 rounded-lg hover:bg-primary-600 transition-colors duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-transform\"\n              >\n                Explore Our Wellness Collection\n              </Link>\n              <Link\n                href=\"/about\"\n                className=\"inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-primary-600 bg-white border-2 border-primary-500 rounded-lg hover:bg-primary-50 transition-colors duration-200\"\n              >\n                Our Story\n              </Link>\n            </motion.div>\n\n            {/* Trust Indicators */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 1.0 }}\n              className=\"mt-12 flex flex-wrap justify-center lg:justify-start gap-6 text-sm text-neutral-500\"\n            >\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-primary-500 rounded-full\"></div>\n                <span>100% Natural</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-secondary-500 rounded-full\"></div>\n                <span>Traditionally Made</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-tertiary-500 rounded-full\"></div>\n                <span>Family Trusted</span>\n              </div>\n            </motion.div>\n          </motion.div>\n\n          {/* Hero Image */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n            className=\"relative\"\n          >\n            <div className=\"relative aspect-square max-w-lg mx-auto\">\n              {/* Hero Image - Replace with your actual image */}\n              <div className=\"absolute inset-0 rounded-3xl shadow-2xl overflow-hidden\">\n                <Image\n                  src=\"/images/hero/wellness-lifestyle.jpg\"\n                  alt=\"Traditional South Indian Wellness Products\"\n                  fill\n                  className=\"object-cover\"\n                  priority\n                  sizes=\"(max-width: 768px) 100vw, 50vw\"\n                  onError={(e) => {\n                    // Fallback to placeholder if image doesn't exist\n                    e.currentTarget.style.display = 'none';\n                    e.currentTarget.nextElementSibling.style.display = 'flex';\n                  }}\n                />\n                {/* Fallback placeholder */}\n                <div className=\"absolute inset-0 bg-gradient-to-br from-primary-100 to-secondary-100 rounded-3xl shadow-2xl\" style={{ display: 'none' }}>\n                  <div className=\"absolute inset-0 flex items-center justify-center\">\n                    <div className=\"text-center\">\n                      <div className=\"w-32 h-32 bg-primary-500 rounded-full mx-auto mb-6 flex items-center justify-center\">\n                        <span className=\"text-4xl text-white\">🌿</span>\n                      </div>\n                      <p className=\"text-neutral-600 font-medium\">\n                        Pure • Natural • Traditional\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n              \n              {/* Floating elements */}\n              <motion.div\n                animate={{ y: [-10, 10, -10] }}\n                transition={{ duration: 4, repeat: Infinity, ease: \"easeInOut\" }}\n                className=\"absolute -top-4 -right-4 w-20 h-20 bg-tertiary-500 rounded-full opacity-20\"\n              />\n              <motion.div\n                animate={{ y: [10, -10, 10] }}\n                transition={{ duration: 3, repeat: Infinity, ease: \"easeInOut\" }}\n                className=\"absolute -bottom-6 -left-6 w-16 h-16 bg-secondary-500 rounded-full opacity-20\"\n              />\n            </div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Scroll indicator */}\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ duration: 1, delay: 1.5 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n      >\n        <motion.div\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n          className=\"w-6 h-10 border-2 border-neutral-400 rounded-full flex justify-center\"\n        >\n          <div className=\"w-1 h-3 bg-neutral-400 rounded-full mt-2\"></div>\n        </motion.div>\n      </motion.div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,MAAM;4BAAU;4BAC7C,WAAU;;8CAEV,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;8CACX;;;;;;8CAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;8CACX;;;;;;8CAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;8CACX;;;;;;8CAMD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;8CAMH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAMZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,IAAI;gDACJ,WAAU;gDACV,QAAQ;gDACR,OAAM;gDACN,SAAS,CAAC;oDACR,iDAAiD;oDACjD,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;oDAChC,EAAE,aAAa,CAAC,kBAAkB,CAAC,KAAK,CAAC,OAAO,GAAG;gDACrD;;;;;;0DAGF,6LAAC;gDAAI,WAAU;gDAA8F,OAAO;oDAAE,SAAS;gDAAO;0DACpI,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;8EAAsB;;;;;;;;;;;0EAExC,6LAAC;gEAAE,WAAU;0EAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDASpD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,GAAG;gDAAC,CAAC;gDAAI;gDAAI,CAAC;6CAAG;wCAAC;wCAC7B,YAAY;4CAAE,UAAU;4CAAG,QAAQ;4CAAU,MAAM;wCAAY;wCAC/D,WAAU;;;;;;kDAEZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,GAAG;gDAAC;gDAAI,CAAC;gDAAI;6CAAG;wCAAC;wCAC5B,YAAY;4CAAE,UAAU;4CAAG,QAAQ;4CAAU,MAAM;wCAAY;wCAC/D,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;oBAAG,OAAO;gBAAI;gBACtC,WAAU;0BAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,GAAG;4BAAC;4BAAG;4BAAI;yBAAE;oBAAC;oBACzB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;oBAC5C,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKzB;KA/JwB", "debugId": null}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/farmnaturelle/src/data/products.ts"], "sourcesContent": ["export interface Product {\n  id: string;\n  name: string;\n  tagline: string;\n  price: number;\n  originalPrice?: number;\n  images: string[];\n  description: string;\n  traditionalMethod: string;\n  benefits: string[];\n  howToUse: string[];\n  ingredients: string[];\n  category: 'wellness' | 'ghee' | 'sweetener';\n  inStock: boolean;\n  variants?: {\n    size: string;\n    price: number;\n    originalPrice?: number;\n  }[];\n}\n\nexport const products: Product[] = [\n  {\n    id: 'shilajit-pure',\n    name: 'Pure Himalayan Shilajit',\n    tagline: 'Ancient Rejuvenator',\n    price: 2499,\n    originalPrice: 2999,\n    images: [\n      '/images/products/shilajit-main.jpg',\n      '/images/products/shilajit-lifestyle.jpg',\n      '/images/products/shilajit-ingredient.jpg',\n      '/images/products/shilajit-packaging.jpg'\n    ],\n    description: 'Sourced from the pristine heights of the Himalayas, our Pure Shilajit is a potent mineral-rich resin that has been revered in Ayurveda for centuries. This \"destroyer of weakness\" is carefully extracted and purified using traditional methods to preserve its natural potency.',\n    traditionalMethod: 'Our Shilajit is sourced from altitudes above 16,000 feet in the Himalayas. The resin is naturally exuded from rocks during summer months and is collected by experienced local harvesters. It undergoes traditional purification processes including sun-drying and filtration through natural materials, ensuring maximum potency while maintaining its authentic properties.',\n    benefits: [\n      'Boosts energy and stamina naturally',\n      'Supports cognitive function and memory',\n      'Rich in fulvic acid and 85+ minerals',\n      'Enhances physical performance',\n      'Supports healthy aging',\n      'Improves nutrient absorption'\n    ],\n    howToUse: [\n      'Take a rice grain-sized portion (300-500mg)',\n      'Dissolve in warm water or milk',\n      'Consume on empty stomach in the morning',\n      'Start with smaller amounts and gradually increase',\n      'Best taken consistently for 2-3 months'\n    ],\n    ingredients: [\n      '100% Pure Himalayan Shilajit Resin',\n      'Fulvic Acid (minimum 60%)',\n      'Natural minerals and trace elements',\n      'No additives or preservatives'\n    ],\n    category: 'wellness',\n    inStock: true,\n    variants: [\n      { size: '10g', price: 1499, originalPrice: 1799 },\n      { size: '20g', price: 2499, originalPrice: 2999 },\n      { size: '50g', price: 5499, originalPrice: 6499 }\n    ]\n  },\n  {\n    id: 'a2-ghee-bilona',\n    name: 'A2 Bilona Ghee',\n    tagline: 'The Golden Elixir',\n    price: 899,\n    originalPrice: 1099,\n    images: [\n      '/images/products/ghee-main.jpg',\n      '/images/products/ghee-bilona-process.jpg',\n      '/images/products/ghee-lifestyle.jpg',\n      '/images/products/ghee-packaging.jpg'\n    ],\n    description: 'Crafted using the ancient Bilona method, our A2 Ghee is made from the milk of indigenous Gir cows. This traditional hand-churning process preserves the natural goodness and creates the most aromatic, golden ghee that has nourished Indian families for generations.',\n    traditionalMethod: 'Our A2 Ghee follows the time-honored Bilona method: Fresh A2 milk from grass-fed Gir cows is first converted to curd using natural cultures. The curd is then hand-churned using a wooden churner (bilona) to extract butter. This butter is slowly heated in copper vessels over wood fire, allowing the moisture to evaporate and creating the purest, most aromatic ghee.',\n    benefits: [\n      'Rich in fat-soluble vitamins A, D, E, K',\n      'Contains beneficial fatty acids',\n      'Supports digestive health',\n      'High smoke point ideal for cooking',\n      'Lactose-free and casein-free',\n      'Boosts immunity and brain function'\n    ],\n    howToUse: [\n      'Use for cooking at high temperatures',\n      'Add to warm milk or tea',\n      'Drizzle over rice, rotis, or vegetables',\n      'Take 1 tsp on empty stomach for digestion',\n      'Store at room temperature'\n    ],\n    ingredients: [\n      '100% Pure A2 Cow Milk',\n      'Made from Gir cow milk only',\n      'No additives or preservatives',\n      'Traditional Bilona method'\n    ],\n    category: 'ghee',\n    inStock: true,\n    variants: [\n      { size: '250ml', price: 499, originalPrice: 599 },\n      { size: '500ml', price: 899, originalPrice: 1099 },\n      { size: '1L', price: 1699, originalPrice: 1999 }\n    ]\n  },\n  {\n    id: 'stevia-natural',\n    name: 'Natural Stevia Powder',\n    tagline: 'Nature\\'s Sweet Gift',\n    price: 349,\n    originalPrice: 449,\n    images: [\n      '/images/products/stevia-main.jpg',\n      '/images/products/stevia-plant.jpg',\n      '/images/products/stevia-lifestyle.jpg',\n      '/images/products/stevia-packaging.jpg'\n    ],\n    description: 'Our Natural Stevia Powder is extracted from organically grown Stevia rebaudiana leaves. This zero-calorie natural sweetener is 200 times sweeter than sugar, making it perfect for those seeking a healthy alternative without compromising on taste.',\n    traditionalMethod: 'Our Stevia is cultivated in organic farms using traditional farming methods. The leaves are harvested at peak sweetness, then naturally dried and processed using water extraction methods. No chemical solvents are used, ensuring you get the purest form of this natural sweetener.',\n    benefits: [\n      'Zero calories and zero glycemic index',\n      'Safe for diabetics',\n      'Does not cause tooth decay',\n      '200x sweeter than regular sugar',\n      'Rich in antioxidants',\n      'Supports weight management'\n    ],\n    howToUse: [\n      'Use 1/4 tsp to replace 1 tsp sugar',\n      'Perfect for beverages, desserts, and baking',\n      'Start with small amounts and adjust to taste',\n      'Mix well to avoid clumping',\n      'Store in a cool, dry place'\n    ],\n    ingredients: [\n      '100% Pure Stevia rebaudiana extract',\n      'Organically grown leaves',\n      'No artificial additives',\n      'No fillers or bulking agents'\n    ],\n    category: 'sweetener',\n    inStock: true,\n    variants: [\n      { size: '50g', price: 249, originalPrice: 299 },\n      { size: '100g', price: 349, originalPrice: 449 },\n      { size: '250g', price: 799, originalPrice: 999 }\n    ]\n  }\n];\n\nexport const getProductById = (id: string): Product | undefined => {\n  return products.find(product => product.id === id);\n};\n\nexport const getProductsByCategory = (category: string): Product[] => {\n  return products.filter(product => product.category === category);\n};\n"], "names": [], "mappings": ";;;;;AAqBO,MAAM,WAAsB;IACjC;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,OAAO;QACP,eAAe;QACf,QAAQ;YACN;YACA;YACA;YACA;SACD;QACD,aAAa;QACb,mBAAmB;QACnB,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,SAAS;QACT,UAAU;YACR;gBAAE,MAAM;gBAAO,OAAO;gBAAM,eAAe;YAAK;YAChD;gBAAE,MAAM;gBAAO,OAAO;gBAAM,eAAe;YAAK;YAChD;gBAAE,MAAM;gBAAO,OAAO;gBAAM,eAAe;YAAK;SACjD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,OAAO;QACP,eAAe;QACf,QAAQ;YACN;YACA;YACA;YACA;SACD;QACD,aAAa;QACb,mBAAmB;QACnB,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,SAAS;QACT,UAAU;YACR;gBAAE,MAAM;gBAAS,OAAO;gBAAK,eAAe;YAAI;YAChD;gBAAE,MAAM;gBAAS,OAAO;gBAAK,eAAe;YAAK;YACjD;gBAAE,MAAM;gBAAM,OAAO;gBAAM,eAAe;YAAK;SAChD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,OAAO;QACP,eAAe;QACf,QAAQ;YACN;YACA;YACA;YACA;SACD;QACD,aAAa;QACb,mBAAmB;QACnB,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,SAAS;QACT,UAAU;YACR;gBAAE,MAAM;gBAAO,OAAO;gBAAK,eAAe;YAAI;YAC9C;gBAAE,MAAM;gBAAQ,OAAO;gBAAK,eAAe;YAAI;YAC/C;gBAAE,MAAM;gBAAQ,OAAO;gBAAK,eAAe;YAAI;SAChD;IACH;CACD;AAEM,MAAM,iBAAiB,CAAC;IAC7B,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AACjD;AAEO,MAAM,wBAAwB,CAAC;IACpC,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;AACzD", "debugId": null}}, {"offset": {"line": 652, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/farmnaturelle/src/components/FeaturedProducts.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { products } from '@/data/products';\nimport { ShoppingBagIcon, StarIcon } from '@heroicons/react/24/solid';\n\nexport default function FeaturedProducts() {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6,\n        ease: \"easeOut\"\n      }\n    }\n  };\n\n  return (\n    <section className=\"py-24 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"font-serif text-3xl sm:text-4xl lg:text-5xl font-bold text-neutral-800 mb-4\">\n            Our Wellness Collection\n          </h2>\n          <p className=\"text-lg text-neutral-600 max-w-2xl mx-auto\">\n            Discover our carefully curated selection of authentic wellness products, \n            each crafted with traditional methods and modern quality standards.\n          </p>\n        </motion.div>\n\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\"\n        >\n          {products.map((product) => (\n            <motion.div\n              key={product.id}\n              variants={itemVariants}\n              className=\"group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden border border-neutral-100\"\n            >\n              {/* Product Image */}\n              <div className=\"relative aspect-square bg-gradient-to-br from-neutral-50 to-neutral-100 overflow-hidden\">\n                <Image\n                  src={product.images[0]}\n                  alt={product.name}\n                  fill\n                  className=\"object-cover group-hover:scale-105 transition-transform duration-300\"\n                  sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n                  onError={(e) => {\n                    // Fallback to placeholder if image doesn't exist\n                    e.currentTarget.style.display = 'none';\n                    e.currentTarget.nextElementSibling.style.display = 'flex';\n                  }}\n                />\n                {/* Fallback placeholder */}\n                <div className=\"absolute inset-0 flex items-center justify-center\" style={{ display: 'none' }}>\n                  <div className=\"text-center\">\n                    <div className=\"w-24 h-24 bg-primary-500 rounded-full mx-auto mb-4 flex items-center justify-center\">\n                      <span className=\"text-2xl text-white\">\n                        {product.category === 'wellness' ? '💎' :\n                         product.category === 'ghee' ? '🥛' : '🌿'}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n                \n                {/* Discount Badge */}\n                {product.originalPrice && (\n                  <div className=\"absolute top-4 left-4 bg-secondary-500 text-white px-3 py-1 rounded-full text-sm font-semibold\">\n                    Save ₹{product.originalPrice - product.price}\n                  </div>\n                )}\n\n                {/* Hover Overlay */}\n                <div className=\"absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center\">\n                  <Link\n                    href={`/products/${product.id}`}\n                    className=\"bg-white text-neutral-800 px-6 py-3 rounded-lg font-semibold hover:bg-neutral-100 transition-colors duration-200 transform translate-y-4 group-hover:translate-y-0 transition-transform\"\n                  >\n                    View Details\n                  </Link>\n                </div>\n              </div>\n\n              {/* Product Info */}\n              <div className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <span className=\"text-sm font-medium text-primary-600 uppercase tracking-wide\">\n                    {product.category}\n                  </span>\n                  <div className=\"flex items-center space-x-1\">\n                    {[...Array(5)].map((_, i) => (\n                      <StarIcon key={i} className=\"w-4 h-4 text-tertiary-500\" />\n                    ))}\n                  </div>\n                </div>\n\n                <h3 className=\"font-serif text-xl font-semibold text-neutral-800 mb-2\">\n                  {product.name}\n                </h3>\n                \n                <p className=\"text-primary-600 font-medium mb-4\">\n                  {product.tagline}\n                </p>\n\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-2\">\n                    <span className=\"text-2xl font-bold text-neutral-800\">\n                      ₹{product.price}\n                    </span>\n                    {product.originalPrice && (\n                      <span className=\"text-lg text-neutral-500 line-through\">\n                        ₹{product.originalPrice}\n                      </span>\n                    )}\n                  </div>\n                  \n                  <button className=\"flex items-center space-x-2 bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors duration-200\">\n                    <ShoppingBagIcon className=\"w-4 h-4\" />\n                    <span className=\"text-sm font-semibold\">Add to Cart</span>\n                  </button>\n                </div>\n\n                {/* Key Benefits */}\n                <div className=\"mt-4 pt-4 border-t border-neutral-100\">\n                  <div className=\"flex flex-wrap gap-2\">\n                    {product.benefits.slice(0, 2).map((benefit, index) => (\n                      <span\n                        key={index}\n                        className=\"text-xs bg-neutral-100 text-neutral-600 px-2 py-1 rounded-full\"\n                      >\n                        {benefit}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* View All Products CTA */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <Link\n            href=\"/products\"\n            className=\"inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-primary-600 bg-primary-50 border-2 border-primary-500 rounded-lg hover:bg-primary-100 transition-colors duration-200\"\n          >\n            View All Products\n          </Link>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAA8E;;;;;;sCAG5F,6LAAC;4BAAE,WAAU;sCAA6C;;;;;;;;;;;;8BAM5D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAET,0HAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,CAAC,wBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,UAAU;4BACV,WAAU;;8CAGV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAK,QAAQ,MAAM,CAAC,EAAE;4CACtB,KAAK,QAAQ,IAAI;4CACjB,IAAI;4CACJ,WAAU;4CACV,OAAM;4CACN,SAAS,CAAC;gDACR,iDAAiD;gDACjD,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;gDAChC,EAAE,aAAa,CAAC,kBAAkB,CAAC,KAAK,CAAC,OAAO,GAAG;4CACrD;;;;;;sDAGF,6LAAC;4CAAI,WAAU;4CAAoD,OAAO;gDAAE,SAAS;4CAAO;sDAC1F,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAK,WAAU;kEACb,QAAQ,QAAQ,KAAK,aAAa,OAClC,QAAQ,QAAQ,KAAK,SAAS,OAAO;;;;;;;;;;;;;;;;;;;;;wCAO7C,QAAQ,aAAa,kBACpB,6LAAC;4CAAI,WAAU;;gDAAiG;gDACvG,QAAQ,aAAa,GAAG,QAAQ,KAAK;;;;;;;sDAKhD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;gDAC/B,WAAU;0DACX;;;;;;;;;;;;;;;;;8CAOL,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DACb,QAAQ,QAAQ;;;;;;8DAEnB,6LAAC;oDAAI,WAAU;8DACZ;2DAAI,MAAM;qDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,gNAAA,CAAA,WAAQ;4DAAS,WAAU;2DAAb;;;;;;;;;;;;;;;;sDAKrB,6LAAC;4CAAG,WAAU;sDACX,QAAQ,IAAI;;;;;;sDAGf,6LAAC;4CAAE,WAAU;sDACV,QAAQ,OAAO;;;;;;sDAGlB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;gEAAsC;gEAClD,QAAQ,KAAK;;;;;;;wDAEhB,QAAQ,aAAa,kBACpB,6LAAC;4DAAK,WAAU;;gEAAwC;gEACpD,QAAQ,aAAa;;;;;;;;;;;;;8DAK7B,6LAAC;oDAAO,WAAU;;sEAChB,6LAAC,8NAAA,CAAA,kBAAe;4DAAC,WAAU;;;;;;sEAC3B,6LAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAK5C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBAC1C,6LAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;;;;;;;;;;;;;2BA5FV,QAAQ,EAAE;;;;;;;;;;8BA0GrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX;KA9KwB", "debugId": null}}, {"offset": {"line": 1045, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/farmnaturelle/src/components/BrandPhilosophy.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { HeartIcon, ShieldCheckIcon, SparklesIcon } from '@heroicons/react/24/outline';\n\nconst philosophies = [\n  {\n    icon: HeartIcon,\n    title: 'Rooted in Tradition',\n    description: 'Every product honors ancient South Indian wellness practices, passed down through generations of wisdom and care.',\n    color: 'primary'\n  },\n  {\n    icon: ShieldCheckIcon,\n    title: 'Uncompromising Purity',\n    description: 'We source only the finest natural ingredients, ensuring every product meets the highest standards of quality and authenticity.',\n    color: 'secondary'\n  },\n  {\n    icon: SparklesIcon,\n    title: 'For Your Family\\'s Well-being',\n    description: 'Crafted with love and care, our products are designed to nourish and support the health of your entire family.',\n    color: 'tertiary'\n  }\n];\n\nexport default function BrandPhilosophy() {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.3\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6,\n        ease: \"easeOut\"\n      }\n    }\n  };\n\n  return (\n    <section className=\"py-24 bg-gradient-to-br from-neutral-50 to-primary-50/30\">\n      <div className=\"max-w-7xl mx-auto px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"font-serif text-3xl sm:text-4xl lg:text-5xl font-bold text-neutral-800 mb-4\">\n            Our Philosophy\n          </h2>\n          <p className=\"text-lg text-neutral-600 max-w-2xl mx-auto\">\n            Built on the foundation of ancient wisdom and modern quality, \n            our philosophy guides everything we do.\n          </p>\n        </motion.div>\n\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          className=\"grid md:grid-cols-3 gap-8\"\n        >\n          {philosophies.map((philosophy, index) => {\n            const IconComponent = philosophy.icon;\n            const colorClasses = {\n              primary: {\n                bg: 'bg-primary-100',\n                icon: 'text-primary-600',\n                border: 'border-primary-200'\n              },\n              secondary: {\n                bg: 'bg-secondary-100',\n                icon: 'text-secondary-600',\n                border: 'border-secondary-200'\n              },\n              tertiary: {\n                bg: 'bg-tertiary-100',\n                icon: 'text-tertiary-600',\n                border: 'border-tertiary-200'\n              }\n            };\n\n            const colors = colorClasses[philosophy.color as keyof typeof colorClasses];\n\n            return (\n              <motion.div\n                key={index}\n                variants={itemVariants}\n                className={`relative bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border-2 ${colors.border} group`}\n              >\n                {/* Icon */}\n                <div className={`w-16 h-16 ${colors.bg} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>\n                  <IconComponent className={`w-8 h-8 ${colors.icon}`} />\n                </div>\n\n                {/* Content */}\n                <h3 className=\"font-serif text-xl font-semibold text-neutral-800 mb-4\">\n                  {philosophy.title}\n                </h3>\n                \n                <p className=\"text-neutral-600 leading-relaxed\">\n                  {philosophy.description}\n                </p>\n\n                {/* Decorative element */}\n                <div className={`absolute top-4 right-4 w-2 h-2 ${colors.bg} rounded-full opacity-50`}></div>\n              </motion.div>\n            );\n          })}\n        </motion.div>\n\n        {/* Additional Content */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"mt-16 text-center\"\n        >\n          <div className=\"bg-white rounded-2xl p-8 shadow-lg border border-neutral-200 max-w-4xl mx-auto\">\n            <h3 className=\"font-serif text-2xl font-semibold text-neutral-800 mb-4\">\n              A Promise to Your Family\n            </h3>\n            <p className=\"text-lg text-neutral-600 leading-relaxed\">\n              At Arogya Saram, we understand that wellness is not just about products—it's about trust, \n              tradition, and the promise of purity. Every jar of ghee, every gram of Shilajit, and every \n              packet of Stevia carries with it our commitment to your family's health and happiness.\n            </p>\n            <div className=\"mt-6 flex justify-center space-x-8 text-sm text-neutral-500\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-primary-500 rounded-full\"></div>\n                <span>Certified Organic</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-secondary-500 rounded-full\"></div>\n                <span>Lab Tested</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-tertiary-500 rounded-full\"></div>\n                <span>Family Approved</span>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,eAAe;IACnB;QACE,MAAM,oNAAA,CAAA,YAAS;QACf,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,gOAAA,CAAA,kBAAe;QACrB,OAAO;QACP,aAAa;QACb,OAAO;IACT;IACA;QACE,MAAM,0NAAA,CAAA,eAAY;QAClB,OAAO;QACP,aAAa;QACb,OAAO;IACT;CACD;AAEc,SAAS;IACtB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAA8E;;;;;;sCAG5F,6LAAC;4BAAE,WAAU;sCAA6C;;;;;;;;;;;;8BAM5D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAET,aAAa,GAAG,CAAC,CAAC,YAAY;wBAC7B,MAAM,gBAAgB,WAAW,IAAI;wBACrC,MAAM,eAAe;4BACnB,SAAS;gCACP,IAAI;gCACJ,MAAM;gCACN,QAAQ;4BACV;4BACA,WAAW;gCACT,IAAI;gCACJ,MAAM;gCACN,QAAQ;4BACV;4BACA,UAAU;gCACR,IAAI;gCACJ,MAAM;gCACN,QAAQ;4BACV;wBACF;wBAEA,MAAM,SAAS,YAAY,CAAC,WAAW,KAAK,CAA8B;wBAE1E,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,UAAU;4BACV,WAAW,CAAC,iGAAiG,EAAE,OAAO,MAAM,CAAC,MAAM,CAAC;;8CAGpI,6LAAC;oCAAI,WAAW,CAAC,UAAU,EAAE,OAAO,EAAE,CAAC,0GAA0G,CAAC;8CAChJ,cAAA,6LAAC;wCAAc,WAAW,CAAC,QAAQ,EAAE,OAAO,IAAI,EAAE;;;;;;;;;;;8CAIpD,6LAAC;oCAAG,WAAU;8CACX,WAAW,KAAK;;;;;;8CAGnB,6LAAC;oCAAE,WAAU;8CACV,WAAW,WAAW;;;;;;8CAIzB,6LAAC;oCAAI,WAAW,CAAC,+BAA+B,EAAE,OAAO,EAAE,CAAC,wBAAwB,CAAC;;;;;;;2BAnBhF;;;;;oBAsBX;;;;;;8BAIF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA0D;;;;;;0CAGxE,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;0CAKxD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB;KAtIwB", "debugId": null}}, {"offset": {"line": 1377, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/farmnaturelle/src/components/BilonaStory.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { ChevronRightIcon } from '@heroicons/react/24/outline';\n\nconst bilonaSteps = [\n  {\n    id: 1,\n    title: 'Fresh A2 Milk Collection',\n    description: 'We start with fresh milk from grass-fed Gir cows, known for their superior A2 protein content. The milk is collected early morning when it\\'s at its purest.',\n    icon: '🐄',\n    details: 'Our partner farms ensure the cows graze freely on natural pastures, producing milk rich in nutrients and free from harmful chemicals.'\n  },\n  {\n    id: 2,\n    title: 'Natural Curd Formation',\n    description: 'The fresh milk is gently heated and cultured using traditional starter cultures to form thick, creamy curd. This process takes 8-12 hours naturally.',\n    icon: '🥛',\n    details: 'We use time-tested fermentation methods that preserve beneficial probiotics and enhance the nutritional profile of the final product.'\n  },\n  {\n    id: 3,\n    title: 'Hand-Churning with <PERSON>ilon<PERSON>',\n    description: 'The curd is hand-churned using a traditional wooden churner called \\'Bilona\\'. This gentle process separates the butter without damaging its molecular structure.',\n    icon: '🔄',\n    details: 'The rhythmic churning process, done in the early morning hours, ensures the butter retains all its natural properties and aromatic compounds.'\n  },\n  {\n    id: 4,\n    title: 'Slow Fire Heating',\n    description: 'The fresh butter is slowly heated in copper vessels over a wood fire. This gentle heating process transforms butter into golden, aromatic ghee.',\n    icon: '🔥',\n    details: 'The slow heating process allows moisture to evaporate completely while preserving the ghee\\'s nutritional value and developing its characteristic nutty aroma.'\n  }\n];\n\nexport default function BilonaStory() {\n  const [activeStep, setActiveStep] = useState(1);\n\n  return (\n    <section className=\"py-24 bg-gradient-to-br from-secondary-50 to-tertiary-50\">\n      <div className=\"max-w-7xl mx-auto px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"font-serif text-3xl sm:text-4xl lg:text-5xl font-bold text-neutral-800 mb-4\">\n            The Sacred Bilona Method\n          </h2>\n          <p className=\"text-lg text-neutral-600 max-w-2xl mx-auto\">\n            Discover the ancient art of ghee-making that transforms simple milk \n            into liquid gold through time-honored traditions.\n          </p>\n        </motion.div>\n\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Interactive Steps */}\n          <div className=\"space-y-4\">\n            {bilonaSteps.map((step) => (\n              <motion.div\n                key={step.id}\n                initial={{ opacity: 0, x: -20 }}\n                whileInView={{ opacity: 1, x: 0 }}\n                transition={{ duration: 0.6, delay: step.id * 0.1 }}\n                viewport={{ once: true }}\n                className={`relative cursor-pointer transition-all duration-300 ${\n                  activeStep === step.id \n                    ? 'bg-white shadow-lg border-2 border-secondary-200' \n                    : 'bg-white/50 hover:bg-white/80 border border-neutral-200'\n                }`}\n                onClick={() => setActiveStep(step.id)}\n              >\n                <div className=\"p-6 rounded-2xl\">\n                  <div className=\"flex items-start space-x-4\">\n                    {/* Step Number & Icon */}\n                    <div className={`flex-shrink-0 w-16 h-16 rounded-2xl flex items-center justify-center text-2xl transition-colors duration-300 ${\n                      activeStep === step.id \n                        ? 'bg-secondary-500 text-white' \n                        : 'bg-neutral-100 text-neutral-600'\n                    }`}>\n                      {step.icon}\n                    </div>\n\n                    {/* Content */}\n                    <div className=\"flex-1\">\n                      <div className=\"flex items-center justify-between\">\n                        <h3 className={`font-serif text-xl font-semibold transition-colors duration-300 ${\n                          activeStep === step.id ? 'text-secondary-700' : 'text-neutral-800'\n                        }`}>\n                          {step.title}\n                        </h3>\n                        <ChevronRightIcon className={`w-5 h-5 transition-all duration-300 ${\n                          activeStep === step.id \n                            ? 'text-secondary-500 rotate-90' \n                            : 'text-neutral-400'\n                        }`} />\n                      </div>\n                      \n                      <p className=\"text-neutral-600 mt-2\">\n                        {step.description}\n                      </p>\n\n                      {/* Expanded Details */}\n                      <AnimatePresence>\n                        {activeStep === step.id && (\n                          <motion.div\n                            initial={{ opacity: 0, height: 0 }}\n                            animate={{ opacity: 1, height: 'auto' }}\n                            exit={{ opacity: 0, height: 0 }}\n                            transition={{ duration: 0.3 }}\n                            className=\"mt-4 pt-4 border-t border-neutral-200\"\n                          >\n                            <p className=\"text-sm text-neutral-500 leading-relaxed\">\n                              {step.details}\n                            </p>\n                          </motion.div>\n                        )}\n                      </AnimatePresence>\n                    </div>\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n\n          {/* Visual Representation */}\n          <motion.div\n            initial={{ opacity: 0, x: 20 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.6 }}\n            viewport={{ once: true }}\n            className=\"relative\"\n          >\n            <div className=\"aspect-square bg-gradient-to-br from-white to-secondary-50 rounded-3xl shadow-2xl p-8 border border-secondary-100\">\n              <AnimatePresence mode=\"wait\">\n                <motion.div\n                  key={activeStep}\n                  initial={{ opacity: 0, scale: 0.8 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  exit={{ opacity: 0, scale: 0.8 }}\n                  transition={{ duration: 0.4 }}\n                  className=\"h-full flex flex-col items-center justify-center text-center\"\n                >\n                  <div className=\"text-8xl mb-6\">\n                    {bilonaSteps.find(step => step.id === activeStep)?.icon}\n                  </div>\n                  <h3 className=\"font-serif text-2xl font-semibold text-neutral-800 mb-4\">\n                    Step {activeStep}\n                  </h3>\n                  <h4 className=\"text-lg font-medium text-secondary-600 mb-4\">\n                    {bilonaSteps.find(step => step.id === activeStep)?.title}\n                  </h4>\n                  <p className=\"text-neutral-600 leading-relaxed\">\n                    {bilonaSteps.find(step => step.id === activeStep)?.description}\n                  </p>\n                </motion.div>\n              </AnimatePresence>\n            </div>\n\n            {/* Decorative Elements */}\n            <motion.div\n              animate={{ rotate: 360 }}\n              transition={{ duration: 20, repeat: Infinity, ease: \"linear\" }}\n              className=\"absolute -top-4 -right-4 w-16 h-16 bg-tertiary-500 rounded-full opacity-20\"\n            />\n            <motion.div\n              animate={{ rotate: -360 }}\n              transition={{ duration: 15, repeat: Infinity, ease: \"linear\" }}\n              className=\"absolute -bottom-6 -left-6 w-12 h-12 bg-secondary-500 rounded-full opacity-20\"\n            />\n          </motion.div>\n        </div>\n\n        {/* Call to Action */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"bg-white rounded-2xl p-8 shadow-lg border border-neutral-200 max-w-2xl mx-auto\">\n            <h3 className=\"font-serif text-2xl font-semibold text-neutral-800 mb-4\">\n              Experience the Difference\n            </h3>\n            <p className=\"text-neutral-600 mb-6\">\n              Taste the purity and richness that only traditional Bilona method can deliver. \n              Our A2 Ghee is a testament to ancient wisdom and modern quality.\n            </p>\n            <button className=\"inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white bg-secondary-500 rounded-lg hover:bg-secondary-600 transition-colors duration-200\">\n              Try Our A2 Bilona Ghee\n            </button>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAMA,MAAM,cAAc;IAClB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,SAAS;IACX;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAA8E;;;;;;sCAG5F,6LAAC;4BAAE,WAAU;sCAA6C;;;;;;;;;;;;8BAM5D,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,KAAK,EAAE,GAAG;oCAAI;oCAClD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAW,CAAC,oDAAoD,EAC9D,eAAe,KAAK,EAAE,GAClB,qDACA,2DACJ;oCACF,SAAS,IAAM,cAAc,KAAK,EAAE;8CAEpC,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAW,CAAC,6GAA6G,EAC5H,eAAe,KAAK,EAAE,GAClB,gCACA,mCACJ;8DACC,KAAK,IAAI;;;;;;8DAIZ,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAW,CAAC,gEAAgE,EAC9E,eAAe,KAAK,EAAE,GAAG,uBAAuB,oBAChD;8EACC,KAAK,KAAK;;;;;;8EAEb,6LAAC,kOAAA,CAAA,mBAAgB;oEAAC,WAAW,CAAC,oCAAoC,EAChE,eAAe,KAAK,EAAE,GAClB,iCACA,oBACJ;;;;;;;;;;;;sEAGJ,6LAAC;4DAAE,WAAU;sEACV,KAAK,WAAW;;;;;;sEAInB,6LAAC,4LAAA,CAAA,kBAAe;sEACb,eAAe,KAAK,EAAE,kBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,SAAS;oEAAE,SAAS;oEAAG,QAAQ;gEAAE;gEACjC,SAAS;oEAAE,SAAS;oEAAG,QAAQ;gEAAO;gEACtC,MAAM;oEAAE,SAAS;oEAAG,QAAQ;gEAAE;gEAC9B,YAAY;oEAAE,UAAU;gEAAI;gEAC5B,WAAU;0EAEV,cAAA,6LAAC;oEAAE,WAAU;8EACV,KAAK,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCArDtB,KAAK,EAAE;;;;;;;;;;sCAkElB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;wCAAC,MAAK;kDACpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,MAAM;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAC/B,YAAY;gDAAE,UAAU;4CAAI;4CAC5B,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;8DACZ,YAAY,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,aAAa;;;;;;8DAErD,6LAAC;oDAAG,WAAU;;wDAA0D;wDAChE;;;;;;;8DAER,6LAAC;oDAAG,WAAU;8DACX,YAAY,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,aAAa;;;;;;8DAErD,6LAAC;oDAAE,WAAU;8DACV,YAAY,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,aAAa;;;;;;;2CAjBhD;;;;;;;;;;;;;;;8CAwBX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,QAAQ;oCAAI;oCACvB,YAAY;wCAAE,UAAU;wCAAI,QAAQ;wCAAU,MAAM;oCAAS;oCAC7D,WAAU;;;;;;8CAEZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,QAAQ,CAAC;oCAAI;oCACxB,YAAY;wCAAE,UAAU;wCAAI,QAAQ;wCAAU,MAAM;oCAAS;oCAC7D,WAAU;;;;;;;;;;;;;;;;;;8BAMhB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA0D;;;;;;0CAGxE,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;0CAIrC,6LAAC;gCAAO,WAAU;0CAAuK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrM;GApKwB;KAAA", "debugId": null}}, {"offset": {"line": 1814, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/farmnaturelle/src/data/testimonials.ts"], "sourcesContent": ["export interface Testimonial {\n  id: string;\n  name: string;\n  location: string;\n  rating: number;\n  review: string;\n  product: string;\n  verified: boolean;\n}\n\nexport const testimonials: Testimonial[] = [\n  {\n    id: '1',\n    name: '<PERSON><PERSON>',\n    location: 'Chennai',\n    rating: 5,\n    review: 'The A2 Bilona Ghee is absolutely divine! You can taste the difference immediately - it\\'s so pure and aromatic. My grandmother would be proud of this traditional quality.',\n    product: 'A2 Bilona Ghee',\n    verified: true\n  },\n  {\n    id: '2',\n    name: '<PERSON><PERSON>',\n    location: 'Bangalore',\n    rating: 5,\n    review: 'I\\'ve been using the Himalayan Shilajit for 3 months now. My energy levels have improved significantly, and I feel more focused throughout the day. Highly recommend!',\n    product: 'Pure Himalayan Shilajit',\n    verified: true\n  },\n  {\n    id: '3',\n    name: '<PERSON>',\n    location: 'Hyderabad',\n    rating: 5,\n    review: 'As a diabetic, finding good natural sweeteners is challenging. This Stevia powder is perfect - no bitter aftertaste and works wonderfully in my morning coffee.',\n    product: 'Natural Stevia Powder',\n    verified: true\n  },\n  {\n    id: '4',\n    name: '<PERSON><PERSON><PERSON>',\n    location: '<PERSON><PERSON>',\n    rating: 5,\n    review: 'The quality of these products is exceptional. You can tell they follow traditional methods. The ghee especially reminds me of what my mother used to make.',\n    product: 'A2 Bilona Ghee',\n    verified: true\n  },\n  {\n    id: '5',\n    name: 'Meera R.',\n    location: 'Coimbatore',\n    rating: 5,\n    review: 'Excellent packaging and fast delivery. The Shilajit is authentic - I can feel the difference in my stamina during workouts. Will definitely reorder.',\n    product: 'Pure Himalayan Shilajit',\n    verified: true\n  },\n  {\n    id: '6',\n    name: 'Suresh N.',\n    location: 'Madurai',\n    rating: 5,\n    review: 'Finally found a trustworthy source for pure A2 ghee. The Bilona method really makes a difference. My family loves the rich taste and aroma.',\n    product: 'A2 Bilona Ghee',\n    verified: true\n  },\n  {\n    id: '7',\n    name: 'Kavitha L.',\n    location: 'Trivandrum',\n    rating: 5,\n    review: 'The Stevia powder has helped me reduce sugar completely. It\\'s so convenient and natural. Perfect for my weight management journey.',\n    product: 'Natural Stevia Powder',\n    verified: true\n  },\n  {\n    id: '8',\n    name: 'Venkat G.',\n    location: 'Vijayawada',\n    rating: 5,\n    review: 'Outstanding quality! The Shilajit is pure and potent. I\\'ve tried many brands, but this one stands out for its authenticity and effectiveness.',\n    product: 'Pure Himalayan Shilajit',\n    verified: true\n  }\n];\n"], "names": [], "mappings": ";;;AAUO,MAAM,eAA8B;IACzC;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;CACD", "debugId": null}}, {"offset": {"line": 1900, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/farmnaturelle/src/components/TestimonialsSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { StarIcon, ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/solid';\nimport { testimonials } from '@/data/testimonials';\n\nexport default function TestimonialsSection() {\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isAutoPlaying, setIsAutoPlaying] = useState(true);\n\n  // Auto-advance testimonials\n  useEffect(() => {\n    if (!isAutoPlaying) return;\n    \n    const interval = setInterval(() => {\n      setCurrentIndex((prev) => (prev + 1) % testimonials.length);\n    }, 5000);\n\n    return () => clearInterval(interval);\n  }, [isAutoPlaying]);\n\n  const nextTestimonial = () => {\n    setCurrentIndex((prev) => (prev + 1) % testimonials.length);\n  };\n\n  const prevTestimonial = () => {\n    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);\n  };\n\n  const goToTestimonial = (index: number) => {\n    setCurrentIndex(index);\n  };\n\n  return (\n    <section className=\"py-24 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"font-serif text-3xl sm:text-4xl lg:text-5xl font-bold text-neutral-800 mb-4\">\n            From Our Customers\n          </h2>\n          <p className=\"text-lg text-neutral-600 max-w-2xl mx-auto\">\n            Hear what families across South India are saying about our authentic wellness products.\n          </p>\n        </motion.div>\n\n        <div className=\"relative max-w-4xl mx-auto\">\n          {/* Main Testimonial Display */}\n          <div \n            className=\"relative bg-gradient-to-br from-primary-50 to-secondary-50 rounded-3xl p-8 lg:p-12 shadow-lg border border-neutral-200 min-h-[300px] flex items-center\"\n            onMouseEnter={() => setIsAutoPlaying(false)}\n            onMouseLeave={() => setIsAutoPlaying(true)}\n          >\n            <AnimatePresence mode=\"wait\">\n              <motion.div\n                key={currentIndex}\n                initial={{ opacity: 0, x: 20 }}\n                animate={{ opacity: 1, x: 0 }}\n                exit={{ opacity: 0, x: -20 }}\n                transition={{ duration: 0.4 }}\n                className=\"w-full text-center\"\n              >\n                {/* Stars */}\n                <div className=\"flex justify-center mb-6\">\n                  {[...Array(testimonials[currentIndex].rating)].map((_, i) => (\n                    <StarIcon key={i} className=\"w-6 h-6 text-tertiary-500\" />\n                  ))}\n                </div>\n\n                {/* Review Text */}\n                <blockquote className=\"text-lg lg:text-xl text-neutral-700 leading-relaxed mb-8 font-medium\">\n                  \"{testimonials[currentIndex].review}\"\n                </blockquote>\n\n                {/* Customer Info */}\n                <div className=\"flex flex-col items-center\">\n                  <div className=\"w-16 h-16 bg-primary-500 rounded-full flex items-center justify-center mb-4\">\n                    <span className=\"text-white font-bold text-xl\">\n                      {testimonials[currentIndex].name.charAt(0)}\n                    </span>\n                  </div>\n                  <h4 className=\"font-semibold text-neutral-800 text-lg\">\n                    {testimonials[currentIndex].name}\n                  </h4>\n                  <p className=\"text-neutral-600 mb-2\">\n                    {testimonials[currentIndex].location}\n                  </p>\n                  <span className=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-700\">\n                    {testimonials[currentIndex].product}\n                  </span>\n                  {testimonials[currentIndex].verified && (\n                    <span className=\"inline-flex items-center mt-2 text-sm text-green-600\">\n                      <svg className=\"w-4 h-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                      </svg>\n                      Verified Purchase\n                    </span>\n                  )}\n                </div>\n              </motion.div>\n            </AnimatePresence>\n\n            {/* Navigation Arrows */}\n            <button\n              onClick={prevTestimonial}\n              className=\"absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center hover:bg-neutral-50 transition-colors duration-200\"\n            >\n              <ChevronLeftIcon className=\"w-6 h-6 text-neutral-600\" />\n            </button>\n            \n            <button\n              onClick={nextTestimonial}\n              className=\"absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center hover:bg-neutral-50 transition-colors duration-200\"\n            >\n              <ChevronRightIcon className=\"w-6 h-6 text-neutral-600\" />\n            </button>\n          </div>\n\n          {/* Dots Indicator */}\n          <div className=\"flex justify-center mt-8 space-x-2\">\n            {testimonials.map((_, index) => (\n              <button\n                key={index}\n                onClick={() => goToTestimonial(index)}\n                className={`w-3 h-3 rounded-full transition-all duration-200 ${\n                  index === currentIndex \n                    ? 'bg-primary-500 w-8' \n                    : 'bg-neutral-300 hover:bg-neutral-400'\n                }`}\n              />\n            ))}\n          </div>\n        </div>\n\n        {/* Additional Testimonials Grid */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"mt-16 grid md:grid-cols-3 gap-6\"\n        >\n          {testimonials.slice(0, 3).map((testimonial, index) => (\n            <div\n              key={testimonial.id}\n              className=\"bg-neutral-50 rounded-2xl p-6 border border-neutral-200 hover:shadow-lg transition-shadow duration-200\"\n            >\n              <div className=\"flex items-center mb-4\">\n                {[...Array(testimonial.rating)].map((_, i) => (\n                  <StarIcon key={i} className=\"w-4 h-4 text-tertiary-500\" />\n                ))}\n              </div>\n              <p className=\"text-neutral-700 text-sm leading-relaxed mb-4\">\n                \"{testimonial.review.length > 100 \n                  ? testimonial.review.substring(0, 100) + '...' \n                  : testimonial.review}\"\n              </p>\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"font-semibold text-neutral-800 text-sm\">\n                    {testimonial.name}\n                  </p>\n                  <p className=\"text-neutral-600 text-xs\">\n                    {testimonial.location}\n                  </p>\n                </div>\n                <span className=\"text-xs bg-primary-100 text-primary-700 px-2 py-1 rounded-full\">\n                  {testimonial.product.split(' ')[0]}\n                </span>\n              </div>\n            </div>\n          ))}\n        </motion.div>\n\n        {/* Trust Indicators */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.6 }}\n          viewport={{ once: true }}\n          className=\"mt-16 text-center\"\n        >\n          <div className=\"bg-gradient-to-r from-primary-50 to-secondary-50 rounded-2xl p-8 border border-neutral-200\">\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-6\">\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-primary-600 mb-2\">500+</div>\n                <div className=\"text-sm text-neutral-600\">Happy Customers</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-secondary-600 mb-2\">4.9★</div>\n                <div className=\"text-sm text-neutral-600\">Average Rating</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-tertiary-600 mb-2\">100%</div>\n                <div className=\"text-sm text-neutral-600\">Natural Products</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-3xl font-bold text-primary-600 mb-2\">24/7</div>\n                <div className=\"text-sm text-neutral-600\">Customer Support</div>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,CAAC,eAAe;YAEpB,MAAM,WAAW;0DAAY;oBAC3B;kEAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,8HAAA,CAAA,eAAY,CAAC,MAAM;;gBAC5D;yDAAG;YAEH;iDAAO,IAAM,cAAc;;QAC7B;wCAAG;QAAC;KAAc;IAElB,MAAM,kBAAkB;QACtB,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,8HAAA,CAAA,eAAY,CAAC,MAAM;IAC5D;IAEA,MAAM,kBAAkB;QACtB,gBAAgB,CAAC,OAAS,CAAC,OAAO,IAAI,8HAAA,CAAA,eAAY,CAAC,MAAM,IAAI,8HAAA,CAAA,eAAY,CAAC,MAAM;IAClF;IAEA,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAA8E;;;;;;sCAG5F,6LAAC;4BAAE,WAAU;sCAA6C;;;;;;;;;;;;8BAK5D,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BACC,WAAU;4BACV,cAAc,IAAM,iBAAiB;4BACrC,cAAc,IAAM,iBAAiB;;8CAErC,6LAAC,4LAAA,CAAA,kBAAe;oCAAC,MAAK;8CACpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,MAAM;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC3B,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;;0DAGV,6LAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM,8HAAA,CAAA,eAAY,CAAC,aAAa,CAAC,MAAM;iDAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrD,6LAAC,gNAAA,CAAA,WAAQ;wDAAS,WAAU;uDAAb;;;;;;;;;;0DAKnB,6LAAC;gDAAW,WAAU;;oDAAuE;oDACzF,8HAAA,CAAA,eAAY,CAAC,aAAa,CAAC,MAAM;oDAAC;;;;;;;0DAItC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEACb,8HAAA,CAAA,eAAY,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;kEAG5C,6LAAC;wDAAG,WAAU;kEACX,8HAAA,CAAA,eAAY,CAAC,aAAa,CAAC,IAAI;;;;;;kEAElC,6LAAC;wDAAE,WAAU;kEACV,8HAAA,CAAA,eAAY,CAAC,aAAa,CAAC,QAAQ;;;;;;kEAEtC,6LAAC;wDAAK,WAAU;kEACb,8HAAA,CAAA,eAAY,CAAC,aAAa,CAAC,OAAO;;;;;;oDAEpC,8HAAA,CAAA,eAAY,CAAC,aAAa,CAAC,QAAQ,kBAClC,6LAAC;wDAAK,WAAU;;0EACd,6LAAC;gEAAI,WAAU;gEAAe,MAAK;gEAAe,SAAQ;0EACxD,cAAA,6LAAC;oEAAK,UAAS;oEAAU,GAAE;oEAAwI,UAAS;;;;;;;;;;;4DACxK;;;;;;;;;;;;;;uCAvCP;;;;;;;;;;8CAgDT,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,8NAAA,CAAA,kBAAe;wCAAC,WAAU;;;;;;;;;;;8CAG7B,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,gOAAA,CAAA,mBAAgB;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKhC,6LAAC;4BAAI,WAAU;sCACZ,8HAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,GAAG,sBACpB,6LAAC;oCAEC,SAAS,IAAM,gBAAgB;oCAC/B,WAAW,CAAC,iDAAiD,EAC3D,UAAU,eACN,uBACA,uCACJ;mCANG;;;;;;;;;;;;;;;;8BAab,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAET,8HAAA,CAAA,eAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,aAAa,sBAC1C,6LAAC;4BAEC,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;8CACZ;2CAAI,MAAM,YAAY,MAAM;qCAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtC,6LAAC,gNAAA,CAAA,WAAQ;4CAAS,WAAU;2CAAb;;;;;;;;;;8CAGnB,6LAAC;oCAAE,WAAU;;wCAAgD;wCACzD,YAAY,MAAM,CAAC,MAAM,GAAG,MAC1B,YAAY,MAAM,CAAC,SAAS,CAAC,GAAG,OAAO,QACvC,YAAY,MAAM;wCAAC;;;;;;;8CAEzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DACV,YAAY,IAAI;;;;;;8DAEnB,6LAAC;oDAAE,WAAU;8DACV,YAAY,QAAQ;;;;;;;;;;;;sDAGzB,6LAAC;4CAAK,WAAU;sDACb,YAAY,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;;2BAvBjC,YAAY,EAAE;;;;;;;;;;8BA+BzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA2C;;;;;;sDAC1D,6LAAC;4CAAI,WAAU;sDAA2B;;;;;;;;;;;;8CAE5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA6C;;;;;;sDAC5D,6LAAC;4CAAI,WAAU;sDAA2B;;;;;;;;;;;;8CAE5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA4C;;;;;;sDAC3D,6LAAC;4CAAI,WAAU;sDAA2B;;;;;;;;;;;;8CAE5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA2C;;;;;;sDAC1D,6LAAC;4CAAI,WAAU;sDAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1D;GA7MwB;KAAA", "debugId": null}}, {"offset": {"line": 2456, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/farmnaturelle/src/components/WellnessJournal.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport { CalendarIcon, ClockIcon, ArrowRightIcon } from '@heroicons/react/24/outline';\n\nconst blogPosts = [\n  {\n    id: 1,\n    title: 'The Ancient Wisdom of Ayurveda: Understanding Doshas in Modern Life',\n    excerpt: 'Discover how the three doshas - <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON> - can guide your wellness journey and help you achieve perfect balance in today\\'s fast-paced world.',\n    category: 'Ayurveda & Tradition',\n    readTime: '8 min read',\n    publishDate: '2024-01-15',\n    image: '/images/blog/ayurveda-doshas.jpg',\n    author: 'Dr. <PERSON><PERSON>'\n  },\n  {\n    id: 2,\n    title: '5 Traditional South Indian Recipes Enhanced with A2 Ghee',\n    excerpt: 'Explore authentic South Indian recipes that showcase the rich flavor and nutritional benefits of traditional A2 Bilona Ghee in everyday cooking.',\n    category: 'Recipes',\n    readTime: '6 min read',\n    publishDate: '2024-01-12',\n    image: '/images/blog/south-indian-recipes.jpg',\n    author: 'Chef <PERSON>'\n  },\n  {\n    id: 3,\n    title: 'Shi<PERSON>jit: The Mountain Gold That Transforms Your Energy',\n    excerpt: 'Uncover the science behind <PERSON><PERSON><PERSON>\\'s powerful effects on energy, stamina, and overall vitality. Learn how this ancient remedy fits into modern wellness.',\n    category: 'Ingredient Spotlight',\n    readTime: '10 min read',\n    publishDate: '2024-01-10',\n    image: '/images/blog/shilajit-benefits.jpg',\n    author: 'Dr. Rajesh Kumar'\n  }\n];\n\nexport default function WellnessJournal() {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 30 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6,\n        ease: \"easeOut\"\n      }\n    }\n  };\n\n  return (\n    <section className=\"py-24 bg-gradient-to-br from-neutral-50 to-primary-50/20\">\n      <div className=\"max-w-7xl mx-auto px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"font-serif text-3xl sm:text-4xl lg:text-5xl font-bold text-neutral-800 mb-4\">\n            Wellness Journal\n          </h2>\n          <p className=\"text-lg text-neutral-600 max-w-2xl mx-auto\">\n            Explore ancient wisdom, modern science, and practical tips for a healthier, \n            more balanced life through our curated wellness content.\n          </p>\n        </motion.div>\n\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true }}\n          className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\"\n        >\n          {blogPosts.map((post) => (\n            <motion.article\n              key={post.id}\n              variants={itemVariants}\n              className=\"group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden border border-neutral-100\"\n            >\n              {/* Featured Image */}\n              <div className=\"relative aspect-[4/3] bg-gradient-to-br from-primary-100 to-secondary-100 overflow-hidden\">\n                {/* Placeholder for blog image */}\n                <div className=\"absolute inset-0 flex items-center justify-center\">\n                  <div className=\"text-center\">\n                    <div className=\"w-16 h-16 bg-primary-500 rounded-full mx-auto mb-4 flex items-center justify-center\">\n                      <span className=\"text-2xl text-white\">\n                        {post.category === 'Ayurveda & Tradition' ? '🕉️' : \n                         post.category === 'Recipes' ? '🍽️' : '💎'}\n                      </span>\n                    </div>\n                    <p className=\"text-neutral-600 font-medium text-sm\">\n                      {post.category}\n                    </p>\n                  </div>\n                </div>\n                \n                {/* Category Badge */}\n                <div className=\"absolute top-4 left-4 bg-white/90 backdrop-blur-sm text-primary-600 px-3 py-1 rounded-full text-sm font-semibold\">\n                  {post.category}\n                </div>\n\n                {/* Hover Overlay */}\n                <div className=\"absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center\">\n                  <Link\n                    href={`/blog/${post.id}`}\n                    className=\"bg-white text-neutral-800 px-6 py-3 rounded-lg font-semibold hover:bg-neutral-100 transition-colors duration-200 transform translate-y-4 group-hover:translate-y-0 transition-transform\"\n                  >\n                    Read Article\n                  </Link>\n                </div>\n              </div>\n\n              {/* Content */}\n              <div className=\"p-6\">\n                <div className=\"flex items-center space-x-4 text-sm text-neutral-500 mb-4\">\n                  <div className=\"flex items-center space-x-1\">\n                    <CalendarIcon className=\"w-4 h-4\" />\n                    <span>{new Date(post.publishDate).toLocaleDateString('en-IN', { \n                      day: 'numeric', \n                      month: 'short', \n                      year: 'numeric' \n                    })}</span>\n                  </div>\n                  <div className=\"flex items-center space-x-1\">\n                    <ClockIcon className=\"w-4 h-4\" />\n                    <span>{post.readTime}</span>\n                  </div>\n                </div>\n\n                <h3 className=\"font-serif text-xl font-semibold text-neutral-800 mb-3 leading-tight group-hover:text-primary-600 transition-colors duration-200\">\n                  <Link href={`/blog/${post.id}`}>\n                    {post.title}\n                  </Link>\n                </h3>\n                \n                <p className=\"text-neutral-600 leading-relaxed mb-4\">\n                  {post.excerpt}\n                </p>\n\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"text-sm text-neutral-500\">\n                    By {post.author}\n                  </div>\n                  \n                  <Link\n                    href={`/blog/${post.id}`}\n                    className=\"inline-flex items-center space-x-1 text-primary-600 hover:text-primary-700 font-semibold text-sm group-hover:translate-x-1 transition-transform duration-200\"\n                  >\n                    <span>Read More</span>\n                    <ArrowRightIcon className=\"w-4 h-4\" />\n                  </Link>\n                </div>\n              </div>\n            </motion.article>\n          ))}\n        </motion.div>\n\n        {/* Categories & CTA */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"mt-16\"\n        >\n          {/* Categories */}\n          <div className=\"flex flex-wrap justify-center gap-4 mb-8\">\n            {['All Articles', 'Ayurveda & Tradition', 'Recipes', 'Ingredient Spotlight', 'Healthy Living'].map((category) => (\n              <Link\n                key={category}\n                href={`/blog?category=${category.toLowerCase().replace(/\\s+/g, '-')}`}\n                className=\"px-4 py-2 bg-white border border-neutral-200 rounded-full text-sm font-medium text-neutral-600 hover:bg-primary-50 hover:border-primary-200 hover:text-primary-600 transition-colors duration-200\"\n              >\n                {category}\n              </Link>\n            ))}\n          </div>\n\n          {/* View All CTA */}\n          <div className=\"text-center\">\n            <Link\n              href=\"/blog\"\n              className=\"inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white bg-primary-500 rounded-lg hover:bg-primary-600 transition-colors duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-transform\"\n            >\n              Explore All Articles\n            </Link>\n          </div>\n        </motion.div>\n\n        {/* Newsletter Signup */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.6 }}\n          viewport={{ once: true }}\n          className=\"mt-16\"\n        >\n          <div className=\"bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl p-8 lg:p-12 text-center text-white\">\n            <h3 className=\"font-serif text-2xl lg:text-3xl font-semibold mb-4\">\n              Stay Connected to Wellness\n            </h3>\n            <p className=\"text-lg mb-8 opacity-90 max-w-2xl mx-auto\">\n              Get weekly wellness tips, traditional recipes, and exclusive insights \n              delivered straight to your inbox.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\">\n              <input\n                type=\"email\"\n                placeholder=\"Enter your email address\"\n                className=\"flex-1 px-4 py-3 rounded-lg text-neutral-800 placeholder-neutral-500 focus:outline-none focus:ring-2 focus:ring-white/50\"\n              />\n              <button className=\"px-6 py-3 bg-white text-primary-600 font-semibold rounded-lg hover:bg-neutral-100 transition-colors duration-200\">\n                Subscribe\n              </button>\n            </div>\n            <p className=\"text-sm opacity-75 mt-4\">\n              No spam, unsubscribe anytime. Your wellness journey starts here.\n            </p>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,YAAY;IAChB;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,aAAa;QACb,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,aAAa;QACb,OAAO;QACP,QAAQ;IACV;IACA;QACE,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,aAAa;QACb,OAAO;QACP,QAAQ;IACV;CACD;AAEc,SAAS;IACtB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAA8E;;;;;;sCAG5F,6LAAC;4BAAE,WAAU;sCAA6C;;;;;;;;;;;;8BAM5D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAET,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,OAAO;4BAEb,UAAU;4BACV,WAAU;;8CAGV,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEACb,KAAK,QAAQ,KAAK,yBAAyB,QAC3C,KAAK,QAAQ,KAAK,YAAY,QAAQ;;;;;;;;;;;kEAG3C,6LAAC;wDAAE,WAAU;kEACV,KAAK,QAAQ;;;;;;;;;;;;;;;;;sDAMpB,6LAAC;4CAAI,WAAU;sDACZ,KAAK,QAAQ;;;;;;sDAIhB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gDACxB,WAAU;0DACX;;;;;;;;;;;;;;;;;8CAOL,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,0NAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;sEACxB,6LAAC;sEAAM,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,CAAC,SAAS;gEAC5D,KAAK;gEACL,OAAO;gEACP,MAAM;4DACR;;;;;;;;;;;;8DAEF,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,6LAAC;sEAAM,KAAK,QAAQ;;;;;;;;;;;;;;;;;;sDAIxB,6LAAC;4CAAG,WAAU;sDACZ,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;0DAC3B,KAAK,KAAK;;;;;;;;;;;sDAIf,6LAAC;4CAAE,WAAU;sDACV,KAAK,OAAO;;;;;;sDAGf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;wDAA2B;wDACpC,KAAK,MAAM;;;;;;;8DAGjB,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;oDACxB,WAAU;;sEAEV,6LAAC;sEAAK;;;;;;sEACN,6LAAC,8NAAA,CAAA,iBAAc;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;2BA1E3B,KAAK,EAAE;;;;;;;;;;8BAmFlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAGV,6LAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAgB;gCAAwB;gCAAW;gCAAwB;6BAAiB,CAAC,GAAG,CAAC,CAAC,yBAClG,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,CAAC,eAAe,EAAE,SAAS,WAAW,GAAG,OAAO,CAAC,QAAQ,MAAM;oCACrE,WAAU;8CAET;mCAJI;;;;;;;;;;sCAUX,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAOL,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqD;;;;;;0CAGnE,6LAAC;gCAAE,WAAU;0CAA4C;;;;;;0CAIzD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,WAAU;;;;;;kDAEZ,6LAAC;wCAAO,WAAU;kDAAmH;;;;;;;;;;;;0CAIvI,6LAAC;gCAAE,WAAU;0CAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnD;KAvMwB", "debugId": null}}]}