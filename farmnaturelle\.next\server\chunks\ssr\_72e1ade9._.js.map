{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/farmnaturelle/src/data/products.ts"], "sourcesContent": ["export interface Product {\n  id: string;\n  name: string;\n  tagline: string;\n  price: number;\n  originalPrice?: number;\n  images: string[];\n  description: string;\n  traditionalMethod: string;\n  benefits: string[];\n  howToUse: string[];\n  ingredients: string[];\n  category: 'wellness' | 'ghee' | 'sweetener';\n  inStock: boolean;\n  variants?: {\n    size: string;\n    price: number;\n    originalPrice?: number;\n  }[];\n}\n\nexport const products: Product[] = [\n  {\n    id: 'shilajit-pure',\n    name: 'Pure Himalayan Shilajit',\n    tagline: 'Ancient Rejuvenator',\n    price: 2499,\n    originalPrice: 2999,\n    images: [\n      '/images/products/shilajit-main.jpg',\n      '/images/products/shilajit-lifestyle.jpg',\n      '/images/products/shilajit-ingredient.jpg',\n      '/images/products/shilajit-packaging.jpg'\n    ],\n    description: 'Sourced from the pristine heights of the Himalayas, our Pure Shilajit is a potent mineral-rich resin that has been revered in Ayurveda for centuries. This \"destroyer of weakness\" is carefully extracted and purified using traditional methods to preserve its natural potency.',\n    traditionalMethod: 'Our Shilajit is sourced from altitudes above 16,000 feet in the Himalayas. The resin is naturally exuded from rocks during summer months and is collected by experienced local harvesters. It undergoes traditional purification processes including sun-drying and filtration through natural materials, ensuring maximum potency while maintaining its authentic properties.',\n    benefits: [\n      'Boosts energy and stamina naturally',\n      'Supports cognitive function and memory',\n      'Rich in fulvic acid and 85+ minerals',\n      'Enhances physical performance',\n      'Supports healthy aging',\n      'Improves nutrient absorption'\n    ],\n    howToUse: [\n      'Take a rice grain-sized portion (300-500mg)',\n      'Dissolve in warm water or milk',\n      'Consume on empty stomach in the morning',\n      'Start with smaller amounts and gradually increase',\n      'Best taken consistently for 2-3 months'\n    ],\n    ingredients: [\n      '100% Pure Himalayan Shilajit Resin',\n      'Fulvic Acid (minimum 60%)',\n      'Natural minerals and trace elements',\n      'No additives or preservatives'\n    ],\n    category: 'wellness',\n    inStock: true,\n    variants: [\n      { size: '10g', price: 1499, originalPrice: 1799 },\n      { size: '20g', price: 2499, originalPrice: 2999 },\n      { size: '50g', price: 5499, originalPrice: 6499 }\n    ]\n  },\n  {\n    id: 'a2-ghee-bilona',\n    name: 'A2 Bilona Ghee',\n    tagline: 'The Golden Elixir',\n    price: 899,\n    originalPrice: 1099,\n    images: [\n      '/images/products/ghee-main.jpg',\n      '/images/products/ghee-bilona-process.jpg',\n      '/images/products/ghee-lifestyle.jpg',\n      '/images/products/ghee-packaging.jpg'\n    ],\n    description: 'Crafted using the ancient Bilona method, our A2 Ghee is made from the milk of indigenous Gir cows. This traditional hand-churning process preserves the natural goodness and creates the most aromatic, golden ghee that has nourished Indian families for generations.',\n    traditionalMethod: 'Our A2 Ghee follows the time-honored Bilona method: Fresh A2 milk from grass-fed Gir cows is first converted to curd using natural cultures. The curd is then hand-churned using a wooden churner (bilona) to extract butter. This butter is slowly heated in copper vessels over wood fire, allowing the moisture to evaporate and creating the purest, most aromatic ghee.',\n    benefits: [\n      'Rich in fat-soluble vitamins A, D, E, K',\n      'Contains beneficial fatty acids',\n      'Supports digestive health',\n      'High smoke point ideal for cooking',\n      'Lactose-free and casein-free',\n      'Boosts immunity and brain function'\n    ],\n    howToUse: [\n      'Use for cooking at high temperatures',\n      'Add to warm milk or tea',\n      'Drizzle over rice, rotis, or vegetables',\n      'Take 1 tsp on empty stomach for digestion',\n      'Store at room temperature'\n    ],\n    ingredients: [\n      '100% Pure A2 Cow Milk',\n      'Made from Gir cow milk only',\n      'No additives or preservatives',\n      'Traditional Bilona method'\n    ],\n    category: 'ghee',\n    inStock: true,\n    variants: [\n      { size: '250ml', price: 499, originalPrice: 599 },\n      { size: '500ml', price: 899, originalPrice: 1099 },\n      { size: '1L', price: 1699, originalPrice: 1999 }\n    ]\n  },\n  {\n    id: 'stevia-natural',\n    name: 'Natural Stevia Powder',\n    tagline: 'Nature\\'s Sweet Gift',\n    price: 349,\n    originalPrice: 449,\n    images: [\n      '/images/products/stevia-main.jpg',\n      '/images/products/stevia-plant.jpg',\n      '/images/products/stevia-lifestyle.jpg',\n      '/images/products/stevia-packaging.jpg'\n    ],\n    description: 'Our Natural Stevia Powder is extracted from organically grown Stevia rebaudiana leaves. This zero-calorie natural sweetener is 200 times sweeter than sugar, making it perfect for those seeking a healthy alternative without compromising on taste.',\n    traditionalMethod: 'Our Stevia is cultivated in organic farms using traditional farming methods. The leaves are harvested at peak sweetness, then naturally dried and processed using water extraction methods. No chemical solvents are used, ensuring you get the purest form of this natural sweetener.',\n    benefits: [\n      'Zero calories and zero glycemic index',\n      'Safe for diabetics',\n      'Does not cause tooth decay',\n      '200x sweeter than regular sugar',\n      'Rich in antioxidants',\n      'Supports weight management'\n    ],\n    howToUse: [\n      'Use 1/4 tsp to replace 1 tsp sugar',\n      'Perfect for beverages, desserts, and baking',\n      'Start with small amounts and adjust to taste',\n      'Mix well to avoid clumping',\n      'Store in a cool, dry place'\n    ],\n    ingredients: [\n      '100% Pure Stevia rebaudiana extract',\n      'Organically grown leaves',\n      'No artificial additives',\n      'No fillers or bulking agents'\n    ],\n    category: 'sweetener',\n    inStock: true,\n    variants: [\n      { size: '50g', price: 249, originalPrice: 299 },\n      { size: '100g', price: 349, originalPrice: 449 },\n      { size: '250g', price: 799, originalPrice: 999 }\n    ]\n  }\n];\n\nexport const getProductById = (id: string): Product | undefined => {\n  return products.find(product => product.id === id);\n};\n\nexport const getProductsByCategory = (category: string): Product[] => {\n  return products.filter(product => product.category === category);\n};\n"], "names": [], "mappings": ";;;;;AAqBO,MAAM,WAAsB;IACjC;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,OAAO;QACP,eAAe;QACf,QAAQ;YACN;YACA;YACA;YACA;SACD;QACD,aAAa;QACb,mBAAmB;QACnB,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,SAAS;QACT,UAAU;YACR;gBAAE,MAAM;gBAAO,OAAO;gBAAM,eAAe;YAAK;YAChD;gBAAE,MAAM;gBAAO,OAAO;gBAAM,eAAe;YAAK;YAChD;gBAAE,MAAM;gBAAO,OAAO;gBAAM,eAAe;YAAK;SACjD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,OAAO;QACP,eAAe;QACf,QAAQ;YACN;YACA;YACA;YACA;SACD;QACD,aAAa;QACb,mBAAmB;QACnB,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,SAAS;QACT,UAAU;YACR;gBAAE,MAAM;gBAAS,OAAO;gBAAK,eAAe;YAAI;YAChD;gBAAE,MAAM;gBAAS,OAAO;gBAAK,eAAe;YAAK;YACjD;gBAAE,MAAM;gBAAM,OAAO;gBAAM,eAAe;YAAK;SAChD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,OAAO;QACP,eAAe;QACf,QAAQ;YACN;YACA;YACA;YACA;SACD;QACD,aAAa;QACb,mBAAmB;QACnB,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,aAAa;YACX;YACA;YACA;YACA;SACD;QACD,UAAU;QACV,SAAS;QACT,UAAU;YACR;gBAAE,MAAM;gBAAO,OAAO;gBAAK,eAAe;YAAI;YAC9C;gBAAE,MAAM;gBAAQ,OAAO;gBAAK,eAAe;YAAI;YAC/C;gBAAE,MAAM;gBAAQ,OAAO;gBAAK,eAAe;YAAI;SAChD;IACH;CACD;AAEM,MAAM,iBAAiB,CAAC;IAC7B,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AACjD;AAEO,MAAM,wBAAwB,CAAC;IACpC,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;AACzD", "debugId": null}}, {"offset": {"line": 208, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/farmnaturelle/src/app/products/page.tsx"], "sourcesContent": ["import { products } from '@/data/products';\nimport Link from 'next/link';\nimport { StarIcon, ShoppingBagIcon } from '@heroicons/react/24/solid';\n\nexport default function ProductsPage() {\n  return (\n    <div className=\"min-h-screen bg-neutral-50 py-24\">\n      <div className=\"max-w-7xl mx-auto px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center mb-16\">\n          <h1 className=\"font-serif text-4xl sm:text-5xl font-bold text-neutral-800 mb-4\">\n            Our Wellness Collection\n          </h1>\n          <p className=\"text-lg text-neutral-600 max-w-2xl mx-auto\">\n            Discover our complete range of authentic wellness products, each crafted with \n            traditional methods and modern quality standards.\n          </p>\n        </div>\n\n        {/* Products Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {products.map((product) => (\n            <div\n              key={product.id}\n              className=\"group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden border border-neutral-100\"\n            >\n              {/* Product Image */}\n              <div className=\"relative aspect-square bg-gradient-to-br from-neutral-50 to-neutral-100 overflow-hidden\">\n                <div className=\"absolute inset-0 flex items-center justify-center\">\n                  <div className=\"text-center\">\n                    <div className=\"w-24 h-24 bg-primary-500 rounded-full mx-auto mb-4 flex items-center justify-center\">\n                      <span className=\"text-2xl text-white\">\n                        {product.category === 'wellness' ? '💎' : \n                         product.category === 'ghee' ? '🥛' : '🌿'}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n                \n                {/* Discount Badge */}\n                {product.originalPrice && (\n                  <div className=\"absolute top-4 left-4 bg-secondary-500 text-white px-3 py-1 rounded-full text-sm font-semibold\">\n                    Save ₹{product.originalPrice - product.price}\n                  </div>\n                )}\n\n                {/* Hover Overlay */}\n                <div className=\"absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center\">\n                  <Link\n                    href={`/products/${product.id}`}\n                    className=\"bg-white text-neutral-800 px-6 py-3 rounded-lg font-semibold hover:bg-neutral-100 transition-colors duration-200 transform translate-y-4 group-hover:translate-y-0 transition-transform\"\n                  >\n                    View Details\n                  </Link>\n                </div>\n              </div>\n\n              {/* Product Info */}\n              <div className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <span className=\"text-sm font-medium text-primary-600 uppercase tracking-wide\">\n                    {product.category}\n                  </span>\n                  <div className=\"flex items-center space-x-1\">\n                    {[...Array(5)].map((_, i) => (\n                      <StarIcon key={i} className=\"w-4 h-4 text-tertiary-500\" />\n                    ))}\n                  </div>\n                </div>\n\n                <h3 className=\"font-serif text-xl font-semibold text-neutral-800 mb-2\">\n                  <Link href={`/products/${product.id}`} className=\"hover:text-primary-600 transition-colors\">\n                    {product.name}\n                  </Link>\n                </h3>\n                \n                <p className=\"text-primary-600 font-medium mb-4\">\n                  {product.tagline}\n                </p>\n\n                <p className=\"text-neutral-600 text-sm leading-relaxed mb-4 line-clamp-3\">\n                  {product.description}\n                </p>\n\n                <div className=\"flex items-center justify-between mb-4\">\n                  <div className=\"flex items-center space-x-2\">\n                    <span className=\"text-2xl font-bold text-neutral-800\">\n                      ₹{product.price}\n                    </span>\n                    {product.originalPrice && (\n                      <span className=\"text-lg text-neutral-500 line-through\">\n                        ₹{product.originalPrice}\n                      </span>\n                    )}\n                  </div>\n                  \n                  <button className=\"flex items-center space-x-2 bg-primary-500 text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors duration-200\">\n                    <ShoppingBagIcon className=\"w-4 h-4\" />\n                    <span className=\"text-sm font-semibold\">Add to Cart</span>\n                  </button>\n                </div>\n\n                {/* Key Benefits */}\n                <div className=\"pt-4 border-t border-neutral-100\">\n                  <div className=\"flex flex-wrap gap-2\">\n                    {product.benefits.slice(0, 2).map((benefit, index) => (\n                      <span\n                        key={index}\n                        className=\"text-xs bg-neutral-100 text-neutral-600 px-2 py-1 rounded-full\"\n                      >\n                        {benefit}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Call to Action */}\n        <div className=\"mt-16 text-center\">\n          <div className=\"bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl p-8 lg:p-12 text-center text-white\">\n            <h3 className=\"font-serif text-2xl lg:text-3xl font-semibold mb-4\">\n              Need Help Choosing?\n            </h3>\n            <p className=\"text-lg mb-8 opacity-90 max-w-2xl mx-auto\">\n              Our wellness experts are here to help you find the perfect products \n              for your health journey. Get personalized recommendations today.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Link\n                href=\"/contact\"\n                className=\"inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-primary-600 bg-white rounded-lg hover:bg-neutral-100 transition-colors duration-200\"\n              >\n                Contact Our Experts\n              </Link>\n              <Link\n                href=\"/about\"\n                className=\"inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white border-2 border-white rounded-lg hover:bg-white/10 transition-colors duration-200\"\n              >\n                Learn Our Story\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAkE;;;;;;sCAGhF,8OAAC;4BAAE,WAAU;sCAA6C;;;;;;;;;;;;8BAO5D,8OAAC;oBAAI,WAAU;8BACZ,uHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,CAAC,wBACb,8OAAC;4BAEC,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEACb,QAAQ,QAAQ,KAAK,aAAa,OAClC,QAAQ,QAAQ,KAAK,SAAS,OAAO;;;;;;;;;;;;;;;;;;;;;wCAO7C,QAAQ,aAAa,kBACpB,8OAAC;4CAAI,WAAU;;gDAAiG;gDACvG,QAAQ,aAAa,GAAG,QAAQ,KAAK;;;;;;;sDAKhD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;gDAC/B,WAAU;0DACX;;;;;;;;;;;;;;;;;8CAOL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DACb,QAAQ,QAAQ;;;;;;8DAEnB,8OAAC;oDAAI,WAAU;8DACZ;2DAAI,MAAM;qDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,6MAAA,CAAA,WAAQ;4DAAS,WAAU;2DAAb;;;;;;;;;;;;;;;;sDAKrB,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;gDAAE,WAAU;0DAC9C,QAAQ,IAAI;;;;;;;;;;;sDAIjB,8OAAC;4CAAE,WAAU;sDACV,QAAQ,OAAO;;;;;;sDAGlB,8OAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;sDAGtB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;;gEAAsC;gEAClD,QAAQ,KAAK;;;;;;;wDAEhB,QAAQ,aAAa,kBACpB,8OAAC;4DAAK,WAAU;;gEAAwC;gEACpD,QAAQ,aAAa;;;;;;;;;;;;;8DAK7B,8OAAC;oDAAO,WAAU;;sEAChB,8OAAC,2NAAA,CAAA,kBAAe;4DAAC,WAAU;;;;;;sEAC3B,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAK5C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACZ,QAAQ,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBAC1C,8OAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;;;;;;;;;;;;;2BApFV,QAAQ,EAAE;;;;;;;;;;8BAkGrB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqD;;;;;;0CAGnE,8OAAC;gCAAE,WAAU;0CAA4C;;;;;;0CAIzD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 598, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/farmnaturelle/node_modules/%40heroicons/react/24/solid/esm/StarIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction StarIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.006 5.404.434c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.434 2.082-5.005Z\",\n    clipRule: \"evenodd\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(StarIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,SAAS,EAChB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,SAAS;QACT,MAAM;QACN,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,UAAU;QACV,GAAG;QACH,UAAU;IACZ;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 638, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/farmnaturelle/node_modules/%40heroicons/react/24/solid/esm/ShoppingBagIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ShoppingBagIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M7.5 6v.75H5.513c-.96 0-1.764.724-1.865 1.679l-1.263 12A1.875 1.875 0 0 0 4.25 22.5h15.5a1.875 1.875 0 0 0 1.865-2.071l-1.263-12a1.875 1.875 0 0 0-1.865-1.679H16.5V6a4.5 4.5 0 1 0-9 0ZM12 3a3 3 0 0 0-3 3v.75h6V6a3 3 0 0 0-3-3Zm-3 8.25a3 3 0 1 0 6 0v-.75a.75.75 0 0 1 1.5 0v.75a4.5 4.5 0 1 1-9 0v-.75a.75.75 0 0 1 1.5 0v.75Z\",\n    clipRule: \"evenodd\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ShoppingBagIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,gBAAgB,EACvB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,SAAS;QACT,MAAM;QACN,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,UAAU;QACV,GAAG;QACH,UAAU;IACZ;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 677, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/farmnaturelle/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 715, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/farmnaturelle/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,GAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}