@import "tailwindcss";

:root {
  --background: #FDFBF8; /* Warm off-white */
  --foreground: #434242; /* Warm dark grey */
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-serif: 'Playfair Display', serif;
}

/* Remove dark mode for this wellness brand */
body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', system-ui, sans-serif;
  line-height: 1.6;
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #22A699;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #1e9488;
}

/* Smooth transitions for all elements */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}
