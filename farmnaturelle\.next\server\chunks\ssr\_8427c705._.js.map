{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/farmnaturelle/src/app/about/page.tsx"], "sourcesContent": ["export default function AboutPage() {\n  return (\n    <div className=\"min-h-screen bg-neutral-50 py-24\">\n      <div className=\"max-w-4xl mx-auto px-6 lg:px-8\">\n        {/* Hero Section */}\n        <div className=\"text-center mb-16\">\n          <h1 className=\"font-serif text-4xl sm:text-5xl font-bold text-neutral-800 mb-6\">\n            Our Story\n          </h1>\n          <p className=\"text-xl text-neutral-600 leading-relaxed\">\n            Rooted in ancient South Indian traditions, <PERSON><PERSON><PERSON><PERSON> was born from a deep \n            reverence for the wisdom of our ancestors and a commitment to bringing authentic \n            wellness to modern families.\n          </p>\n        </div>\n\n        {/* Story Content */}\n        <div className=\"prose prose-lg max-w-none\">\n          <div className=\"bg-white rounded-2xl p-8 lg:p-12 shadow-lg border border-neutral-200 mb-12\">\n            <h2 className=\"font-serif text-2xl font-semibold text-neutral-800 mb-6\">\n              The Beginning of Our Journey\n            </h2>\n            <p className=\"text-neutral-600 leading-relaxed mb-6\">\n              In the heart of South India, where ancient traditions meet modern aspirations, \n              <PERSON><PERSON><PERSON> was conceived with a simple yet profound mission: to preserve and \n              share the timeless wellness practices that have nourished generations of families.\n            </p>\n            <p className=\"text-neutral-600 leading-relaxed mb-6\">\n              Our founders, deeply rooted in Ayurvedic traditions and modern nutritional science, \n              recognized that in our fast-paced world, families were losing touch with the pure, \n              natural products that had sustained their ancestors for centuries.\n            </p>\n            <p className=\"text-neutral-600 leading-relaxed\">\n              Thus began our quest to source, craft, and deliver the finest wellness products \n              using methods passed down through generations, ensuring that every family can \n              access the healing power of nature's bounty.\n            </p>\n          </div>\n\n          <div className=\"grid md:grid-cols-2 gap-8 mb-12\">\n            <div className=\"bg-primary-50 rounded-2xl p-8 border border-primary-200\">\n              <h3 className=\"font-serif text-xl font-semibold text-neutral-800 mb-4\">\n                Our Mission\n              </h3>\n              <p className=\"text-neutral-600 leading-relaxed\">\n                To bridge the gap between ancient wisdom and modern wellness by providing \n                authentic, traditionally-made products that honor our heritage while meeting \n                contemporary quality standards.\n              </p>\n            </div>\n\n            <div className=\"bg-secondary-50 rounded-2xl p-8 border border-secondary-200\">\n              <h3 className=\"font-serif text-xl font-semibold text-neutral-800 mb-4\">\n                Our Vision\n              </h3>\n              <p className=\"text-neutral-600 leading-relaxed\">\n                To become the most trusted name in authentic wellness products, helping \n                families across India and beyond reconnect with the natural healing \n                traditions of their ancestors.\n              </p>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-2xl p-8 lg:p-12 shadow-lg border border-neutral-200 mb-12\">\n            <h2 className=\"font-serif text-2xl font-semibold text-neutral-800 mb-6\">\n              Our Partnership with Farm Naturelle\n            </h2>\n            <p className=\"text-neutral-600 leading-relaxed mb-6\">\n              We are proud to partner with Farm Naturelle, a pioneer in organic and natural \n              products, to bring you the highest quality wellness solutions. This collaboration \n              ensures that every product meets stringent quality standards while maintaining \n              the authenticity of traditional preparation methods.\n            </p>\n            <p className=\"text-neutral-600 leading-relaxed\">\n              Together, we work directly with farmers and artisans who have preserved ancient \n              techniques, ensuring fair trade practices and sustainable sourcing that benefits \n              both communities and consumers.\n            </p>\n          </div>\n\n          <div className=\"bg-gradient-to-br from-tertiary-50 to-primary-50 rounded-2xl p-8 lg:p-12 border border-tertiary-200 mb-12\">\n            <h2 className=\"font-serif text-2xl font-semibold text-neutral-800 mb-6\">\n              Our Commitment to Quality\n            </h2>\n            <div className=\"grid md:grid-cols-3 gap-6\">\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-primary-500 rounded-full mx-auto mb-4 flex items-center justify-center\">\n                  <span className=\"text-2xl text-white\">🌱</span>\n                </div>\n                <h4 className=\"font-semibold text-neutral-800 mb-2\">100% Natural</h4>\n                <p className=\"text-sm text-neutral-600\">\n                  No artificial additives, preservatives, or chemicals\n                </p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-secondary-500 rounded-full mx-auto mb-4 flex items-center justify-center\">\n                  <span className=\"text-2xl text-white\">🏺</span>\n                </div>\n                <h4 className=\"font-semibold text-neutral-800 mb-2\">Traditional Methods</h4>\n                <p className=\"text-sm text-neutral-600\">\n                  Time-honored techniques preserved for generations\n                </p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-tertiary-500 rounded-full mx-auto mb-4 flex items-center justify-center\">\n                  <span className=\"text-2xl text-white\">🔬</span>\n                </div>\n                <h4 className=\"font-semibold text-neutral-800 mb-2\">Lab Tested</h4>\n                <p className=\"text-sm text-neutral-600\">\n                  Rigorous quality testing for purity and potency\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-2xl p-8 lg:p-12 shadow-lg border border-neutral-200\">\n            <h2 className=\"font-serif text-2xl font-semibold text-neutral-800 mb-6\">\n              Looking Forward\n            </h2>\n            <p className=\"text-neutral-600 leading-relaxed mb-6\">\n              As we continue to grow, our commitment remains unchanged: to honor the wisdom \n              of our ancestors while embracing the possibilities of tomorrow. We are constantly \n              researching and developing new products that stay true to traditional methods \n              while meeting the evolving needs of modern families.\n            </p>\n            <p className=\"text-neutral-600 leading-relaxed\">\n              Join us on this journey of wellness, tradition, and authentic living. Together, \n              we can create a healthier, more connected world—one family at a time.\n            </p>\n          </div>\n        </div>\n\n        {/* Call to Action */}\n        <div className=\"text-center mt-16\">\n          <div className=\"bg-gradient-to-r from-primary-500 to-secondary-500 rounded-2xl p-8 lg:p-12 text-white\">\n            <h3 className=\"font-serif text-2xl lg:text-3xl font-semibold mb-4\">\n              Ready to Begin Your Wellness Journey?\n            </h3>\n            <p className=\"text-lg mb-8 opacity-90\">\n              Discover our collection of authentic wellness products and experience \n              the difference that tradition and quality make.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <a\n                href=\"/products\"\n                className=\"inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-primary-600 bg-white rounded-lg hover:bg-neutral-100 transition-colors duration-200\"\n              >\n                Shop Our Products\n              </a>\n              <a\n                href=\"/contact\"\n                className=\"inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white border-2 border-white rounded-lg hover:bg-white/10 transition-colors duration-200\"\n              >\n                Contact Us\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAkE;;;;;;sCAGhF,8OAAC;4BAAE,WAAU;sCAA2C;;;;;;;;;;;;8BAQ1D,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0D;;;;;;8CAGxE,8OAAC;oCAAE,WAAU;8CAAwC;;;;;;8CAKrD,8OAAC;oCAAE,WAAU;8CAAwC;;;;;;8CAKrD,8OAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;;sCAOlD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyD;;;;;;sDAGvE,8OAAC;4CAAE,WAAU;sDAAmC;;;;;;;;;;;;8CAOlD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyD;;;;;;sDAGvE,8OAAC;4CAAE,WAAU;sDAAmC;;;;;;;;;;;;;;;;;;sCAQpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0D;;;;;;8CAGxE,8OAAC;oCAAE,WAAU;8CAAwC;;;;;;8CAMrD,8OAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;;sCAOlD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0D;;;;;;8CAGxE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAsB;;;;;;;;;;;8DAExC,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;8DACpD,8OAAC;oDAAE,WAAU;8DAA2B;;;;;;;;;;;;sDAI1C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAsB;;;;;;;;;;;8DAExC,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;8DACpD,8OAAC;oDAAE,WAAU;8DAA2B;;;;;;;;;;;;sDAI1C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAsB;;;;;;;;;;;8DAExC,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;8DACpD,8OAAC;oDAAE,WAAU;8DAA2B;;;;;;;;;;;;;;;;;;;;;;;;sCAO9C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0D;;;;;;8CAGxE,8OAAC;oCAAE,WAAU;8CAAwC;;;;;;8CAMrD,8OAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;;;;;;;;8BAQpD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqD;;;;;;0CAGnE,8OAAC;gCAAE,WAAU;0CAA0B;;;;;;0CAIvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 486, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/farmnaturelle/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/farmnaturelle/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,MAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}